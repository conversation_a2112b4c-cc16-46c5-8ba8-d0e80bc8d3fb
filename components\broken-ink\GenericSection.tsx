"use client";

import React, { useState } from "react";
import { Phone } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { UserProfile, SectionItem } from "@/types/user";
import { PLACEHOLDER_IMAGES } from "@/types/constants";
import { getButtonConfig } from "@/lib/buttonUtils";
import { CustomCarousel } from "@/components/ui/carousel";

interface GenericSectionProps {
  profile: UserProfile;
}

const GenericSection = ({ profile }: GenericSectionProps) => {
  const [isLoading, setIsLoading] = useState(true);

  React.useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 100);
    return () => clearTimeout(timer);
  }, []);

  if (!profile.genericSection?.enabled) {
    return null;
  }

  const sectionData = profile.genericSection;
  const buttonConfig = getButtonConfig("generic", sectionData.buttonConfig);

  const handleButtonClick = (url: string, event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (url && url !== "#" && url.startsWith("http")) {
      window.open(url, "_blank", "noopener,noreferrer");
    }
  };

  const renderCard = (item: SectionItem) => {
    const hasPrimaryButton =
      item.primaryButton?.url && item.primaryButton.url !== "#";
    const hasSecondaryButton =
      item.secondaryButton?.url && item.secondaryButton.url !== "#";

    return (
      <div
        key={item.id}
        className="relative overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 transform-gpu group rounded-xl h-96 sm:h-80 lg:h-96"
      >
        <Image
          src={item.image || PLACEHOLDER_IMAGES.GALLERY_IMAGE}
          alt={item.title}
          fill
          className="object-cover transition-transform duration-700 group-hover:scale-110"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          priority={false}
        />
        {/* Enhanced gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"></div>

        {/* Badge for generic content */}
        {buttonConfig.showBadge && (
          <div className="absolute top-6 right-6">
            <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center transform transition-all duration-300 group-hover:scale-110 shadow-lg">
              <span className="text-white text-base font-bold">{item.id}</span>
            </div>
          </div>
        )}

        {/* Content positioned at bottom */}
        <div className="absolute bottom-0 left-0 right-0 p-6 sm:p-8">
          <h3 className="text-white font-bold text-xl sm:text-2xl mb-2 drop-shadow-lg">
            {item.title}
          </h3>
          <p className="text-white/90 text-sm sm:text-base mb-4 leading-relaxed drop-shadow-sm line-clamp-3">
            {item.description}
          </p>

          <div className="flex gap-3">
            {hasPrimaryButton && (
              <Button
                variant="default"
                size="lg"
                className="flex-1 h-14 bg-white/20 hover:bg-white/30 backdrop-blur-sm hover:scale-105 transform transition-all duration-300 shadow-lg hover:shadow-xl border-0 rounded-2xl text-white"
                onClick={(e) =>
                  handleButtonClick(item.primaryButton?.url || "#", e)
                }
              >
                {buttonConfig.primaryButtonText}
              </Button>
            )}
            {hasSecondaryButton && (
              <Button
                variant="outline"
                size="lg"
                className={`${
                  hasPrimaryButton ? "w-14 h-14 p-0" : "flex-1"
                } bg-white/10 border-white/30 hover:bg-white/20 backdrop-blur-sm hover:scale-105 transform transition-all duration-300 shadow-lg rounded-2xl text-white`}
                onClick={(e) =>
                  handleButtonClick(item.secondaryButton?.url || "#", e)
                }
              >
                {item.secondaryButton?.icon ? (
                  <i
                    className={`${item.secondaryButton.icon} ${
                      hasPrimaryButton ? "text-lg" : "mr-2"
                    } group-hover:scale-110 transition-transform duration-300`}
                    aria-hidden="true"
                  />
                ) : (
                  <Phone
                    className={`${
                      hasPrimaryButton ? "w-5 h-5" : "w-4 h-4 mr-2"
                    }`}
                  />
                )}
                {!hasPrimaryButton && buttonConfig.secondaryButtonText}
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <section className="py-16 sm:py-24 bg-black" id="generic">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 flex flex-col items-center">
        <div className="text-center mb-12">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {sectionData.title}
          </h2>
          {sectionData.description && (
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              {sectionData.description}
            </p>
          )}
        </div>

        {/* Carousel */}
        <CustomCarousel
          align="center"
          loop={false}
          showNavigation={true}
          showDots={true}
          isLoading={isLoading}
          gap="0.75rem"
          breakpoints={{
            default: { slidesPerView: "80%" },
            sm: { slidesPerView: "65%" },
            md: { slidesPerView: "50%" },
            lg: { slidesPerView: "360px" },
          }}
          navigationLabels={{
            prev: "Conteúdo anterior",
            next: "Próximo conteúdo",
          }}
          dotLabel={(index, total) =>
            `Ir para conteúdo ${index + 1} de ${total}`
          }
          ariaLabel="Conteúdo genérico"
          className="rounded-2xl"
        >
          {sectionData.items.map((item) => renderCard(item))}
        </CustomCarousel>
      </div>
    </section>
  );
};

export default GenericSection;

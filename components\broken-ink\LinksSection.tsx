"use client";

import React, { useCallback, useState } from "react";
import { getIconComponent } from "@/lib/iconUtils";
import { UserProfile } from "@/types/user";
import { transformLinksData } from "@/lib/brokenInkUtils";
import { CustomCarousel } from "@/components/ui/carousel";

interface LinksSectionProps {
  profile: UserProfile;
}

const LinksSection = ({ profile }: LinksSectionProps) => {
  const linksData = transformLinksData(profile);
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading
  React.useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  // Enhanced link click handler
  const handleLinkClick = useCallback(
    async (url: string, event: React.MouseEvent) => {
      try {
        if (
          url.startsWith("http") ||
          url.startsWith("mailto:") ||
          url.startsWith("tel:")
        ) {
          window.open(url, "_blank", "noopener,noreferrer");
        } else if (url.startsWith("#")) {
          event.preventDefault();
          const element = document.querySelector(url);
          if (element) {
            element.scrollIntoView({
              behavior: "smooth",
              block: "start",
              inline: "nearest",
            });
          }
        }
      } catch (error) {
        console.error("Error opening link:", error);
      }
    },
    []
  );

  // Don't render if no links and not loading
  if (!isLoading && linksData.links.length === 0) {
    return null;
  }

  return (
    <section className="pb-16 sm:pb-24 bg-black max-w-7xl mx-auto" id="links">
      <div className="px-4 sm:px-6 lg:px-8 flex flex-col items-center">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            {/* <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {linksData.title}
          </h2> */}
            {linksData.description && (
              <p className="text-gray-300 text-lg max-w-2xl mx-auto">
                {linksData.description}
              </p>
            )}
          </div>

          {/* Horizontal Scroll Carousel for All Screen Sizes */}
          <CustomCarousel
            align="center"
            loop={false}
            showNavigation={false}
            showDots={true}
            isLoading={isLoading}
            loadingSlides={3}
            gap="1rem"
            breakpoints={{
              default: { slidesPerView: "320px" },
              sm: { slidesPerView: "360px" },
              md: { slidesPerView: "380px" },
            }}
            slideClassName="p-2"
            navigationLabels={{
              prev: "Link anterior",
              next: "Próximo link",
            }}
            dotLabel={(index, total) => `Ir para link ${index + 1} de ${total}`}
            ariaLabel="Links do perfil"
          >
            {linksData.links.map((link, index) => {
              const IconComponent = getIconComponent(link.iconName);
              return (
                <LinkCard
                  key={index}
                  icon={<IconComponent className="h-6 w-6 text-white" />}
                  title={link.text}
                  description={link.description}
                  onClick={(e) => handleLinkClick(link.url, e)}
                  isLoading={isLoading}
                />
              );
            })}
          </CustomCarousel>
        </div>
      </div>
    </section>
  );
};

// LinkCard Component
interface LinkCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  onClick: (event: React.MouseEvent) => void;
  isLoading?: boolean;
}

const LinkCard: React.FC<LinkCardProps> = ({
  icon,
  title,
  onClick,
  isLoading = false,
}) => {
  if (isLoading) {
    return (
      <div className="w-full animate-pulse">
        <div className="rounded-3xl bg-gray-800/50 backdrop-blur-3xl w-full p-6 ring-1 ring-black/10">
          <div className="flex items-center gap-4">
            <div className="bg-gray-700/50 p-4 rounded-3xl w-14 h-14 flex-shrink-0"></div>
            <div className="space-y-2 flex-1">
              <div className="h-5 bg-gray-700/50 rounded w-3/4"></div>
              <div className="h-4 bg-gray-700/50 rounded w-full"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <button
      className="group relative flex items-center justify-between rounded-3xl bg-custom backdrop-blur-3xl w-full p-6 ring-1 ring-black/10 transition-all hover:ring-white/20 hover:scale-[1.02] active:scale-[0.98] text-left"
      onClick={onClick}
    >
      <div className="flex items-center gap-4 w-full">
        <div className="bg-black/40 p-4 rounded-3xl transition-all group-hover:bg-black/60 flex-shrink-0">
          {icon}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-white text-lg font-semibold mb-1">{title}</h3>
        </div>
      </div>
    </button>
  );
};

export default LinksSection;

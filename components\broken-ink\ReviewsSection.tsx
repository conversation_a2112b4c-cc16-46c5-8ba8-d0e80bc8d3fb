import React from "react";
import { UserProfile } from "@/types/user";
import { CustomCarousel } from "@/components/ui/carousel";

interface ReviewsSectionProps {
  profile: UserProfile;
}

const ReviewsSection = ({ profile }: ReviewsSectionProps) => {
  // Check if reviews are enabled and exist
  if (!profile.reviews?.enabled || !profile.reviews?.reviews?.length) {
    return null;
  }

  const reviews = profile.reviews;

  return (
    <section className="py-16 sm:py-24 bg-black overflow-hidden" id="reviews">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {reviews.title || "Avaliações"}
          </h2>
          {reviews.description && (
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              {reviews.description}
            </p>
          )}
        </div>

        <CustomCarousel
          align="start"
          loop={true}
          showNavigation={true}
          showDots={true}
          gap="0.75rem"
          breakpoints={{
            default: { slidesPerView: "calc(100% - 2rem)" },
            sm: { slidesPerView: "calc(70% - 1rem)" },
            md: { slidesPerView: "calc(50% - 1rem)" },
            lg: { slidesPerView: "360px" },
          }}
          navigationLabels={{
            prev: "Avaliação anterior",
            next: "Próxima avaliação",
          }}
          dotLabel={(index, total) =>
            `Ir para avaliação ${index + 1} de ${total}`
          }
          ariaLabel="Avaliações de clientes"
          className="rounded-xl"
        >
          {reviews.reviews.map((review, index) => (
            <div
              key={review.id}
              className="bg-custom rounded-3xl p-4 sm:p-6 h-full flex flex-col border border-gray-950 transition-all duration-300 hover:scale-[1.02] transform-gpu w-full"
            >
              <div className="flex items-start space-x-4 mb-4">
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2 mb-2">
                    <h4 className="font-semibold text-white text-base truncate">
                      {review.name}
                    </h4>
                    <div
                      className="flex text-yellow-400 shrink-0"
                      aria-label={`${review.rating} estrelas`}
                    >
                      {[...Array(review.rating)].map((_, i) => (
                        <i
                          key={i}
                          className="fas fa-star text-sm transition-transform duration-300"
                          style={{ animationDelay: `${i * 0.1}s` }}
                          aria-hidden="true"
                        ></i>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Review comment */}
              <blockquote className="flex-1 flex items-center">
                <p className="text-gray-300 text-sm leading-relaxed italic">
                  &ldquo;{review.comment}&rdquo;
                </p>
              </blockquote>

              {/* Decorative quote mark */}
              <div className="mt-4 flex justify-end opacity-20 transition-opacity duration-300">
                <i className="fas fa-quote-right text-2xl text-gray-500"></i>
              </div>
            </div>
          ))}
        </CustomCarousel>
      </div>
    </section>
  );
};

export default ReviewsSection;

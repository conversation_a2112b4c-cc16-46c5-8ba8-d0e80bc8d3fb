import React from "react";
import ArtistProfile from "./ArtistProfile";
import { UserProfile } from "@/types/user";
import { transformTeamData } from "@/lib/brokenInkUtils";
import { CustomCarousel } from "@/components/ui/carousel";

interface TeamSectionProps {
  profile: UserProfile;
}

const TeamSection = ({ profile }: TeamSectionProps) => {
  const artistsData = transformTeamData(profile);

  if (!artistsData.enabled) {
    return null;
  }

  return (
    <section className="py-16 sm:py-24 bg-black mx-auto max-w-7xl" id="artists">
      <div className="px-4 sm:px-6 lg:px-8 flex flex-col items-center">
        <div className="text-center mb-12">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {artistsData.title}
          </h2>
        </div>

        <CustomCarousel
          align="start"
          loop={true}
          showNavigation={false}
          showDots={true}
          gap="0.75rem"
          breakpoints={{
            default: { slidesPerView: "calc(100% - 2rem)" },
            sm: { slidesPerView: "calc(70% - 1rem)" },
            md: { slidesPerView: "calc(50% - 1rem)" },
            lg: { slidesPerView: "360px" },
          }}
          slideClassName="mx-auto"
          navigationLabels={{
            prev: "Artista anterior",
            next: "Próximo artista",
          }}
          dotLabel={(index, total) =>
            `Ir para artista ${index + 1} de ${total}`
          }
          ariaLabel="Equipe de artistas"
        >
          {artistsData.artists.map((artist, index) => (
            <ArtistProfile
              key={`${artist.name}-${index}`}
              imageUrl={artist.imageUrl}
              name={artist.name}
              specialty={artist.specialty}
              url={artist.url}
            />
          ))}
        </CustomCarousel>
      </div>
    </section>
  );
};

export default TeamSection;

import React from "react";
import { cn } from "@/lib/utils";
import { CarouselDotsProps } from "./carousel.types";

/**
 * CarouselDots component - Progress indicators for carousel
 * 
 * Features:
 * - Smooth opacity and scale transitions
 * - Hover effects with opacity changes
 * - C<PERSON> to navigate to specific slide
 * - Accessible with proper ARIA labels
 * - Responsive design
 */
const CarouselDots: React.FC<CarouselDotsProps> = ({
  slideCount,
  selectedIndex,
  onDotClick,
  dotLabel,
  className,
}) => {
  // Don't render if only one slide
  if (slideCount <= 1) {
    return null;
  }

  const defaultDotLabel = (index: number, total: number) =>
    `Ir para slide ${index + 1} de ${total}`;

  const getDotLabel = dotLabel || defaultDotLabel;

  return (
    <div
      className={cn(
        "flex justify-center gap-2",
        className
      )}
      role="tablist"
      aria-label="Navegação do carousel"
    >
      {Array.from({ length: slideCount }, (_, index) => (
        <button
          key={index}
          role="tab"
          aria-selected={selectedIndex === index}
          aria-label={getDotLabel(index, slideCount)}
          className={cn(
            "w-2 h-2 rounded-full transition-all duration-300 cursor-pointer bg-white",
            "hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black",
            selectedIndex === index
              ? "opacity-100 scale-125"
              : "opacity-40 hover:opacity-60"
          )}
          onClick={() => onDotClick(index)}
          onMouseEnter={(e) => {
            if (selectedIndex !== index) {
              e.currentTarget.style.opacity = "0.6";
            }
          }}
          onMouseLeave={(e) => {
            if (selectedIndex !== index) {
              e.currentTarget.style.opacity = "0.4";
            }
          }}
        />
      ))}
    </div>
  );
};

export default React.memo(CarouselDots);

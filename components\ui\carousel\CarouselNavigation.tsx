import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { CarouselNavigationProps, DEFAULT_NAVIGATION_LABELS } from "./carousel.types";

/**
 * CarouselNavigation component - Previous/Next navigation buttons
 * 
 * Features:
 * - Disabled state handling with visual feedback
 * - Proper ARIA labels for accessibility
 * - Backdrop blur styling with hover effects
 * - Scale transform animations
 * - Circular button design
 * - Keyboard navigation support
 */
const CarouselNavigation: React.FC<CarouselNavigationProps> = ({
  onPrevious,
  onNext,
  canScrollPrev,
  canScrollNext,
  labels = DEFAULT_NAVIGATION_LABELS,
  className,
}) => {
  return (
    <div
      className={cn(
        "flex justify-center gap-3",
        className
      )}
    >
      <Button
        variant="outline"
        size="sm"
        onClick={onPrevious}
        disabled={!canScrollPrev}
        className={cn(
          "w-12 h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-200",
          "bg-white/20 border-white/30 hover:bg-white/30 text-white",
          "hover:scale-110 transform focus:scale-110",
          "disabled:opacity-30 disabled:cursor-not-allowed disabled:hover:scale-100",
          "focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black"
        )}
        aria-label={labels.prev}
      >
        <ChevronLeft className="w-5 h-5" />
      </Button>
      
      <Button
        variant="outline"
        size="sm"
        onClick={onNext}
        disabled={!canScrollNext}
        className={cn(
          "w-12 h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-200",
          "bg-white/20 border-white/30 hover:bg-white/30 text-white",
          "hover:scale-110 transform focus:scale-110",
          "disabled:opacity-30 disabled:cursor-not-allowed disabled:hover:scale-100",
          "focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black"
        )}
        aria-label={labels.next}
      >
        <ChevronRight className="w-5 h-5" />
      </Button>
    </div>
  );
};

export default React.memo(CarouselNavigation);

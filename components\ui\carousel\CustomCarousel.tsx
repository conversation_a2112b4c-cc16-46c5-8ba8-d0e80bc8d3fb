"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { cn } from "@/lib/utils";
import CarouselNavigation from "./CarouselNavigation";
import CarouselDots from "./CarouselDots";
import {
  CustomCarouselProps,
  DEFAULT_CAROUSEL_CONFIG,
  DEFAULT_BREAKPOINTS,
  DEFAULT_NAVIGATION_LABELS,
  DEFAULT_AUTOPLAY_CONFIG,
  AutoplayConfig,
} from "./carousel.types";

/**
 * CustomCarousel - Reusable Embla carousel component
 * 
 * Features:
 * - Configurable navigation buttons and progress dots
 * - Auto-play with pause on hover
 * - Responsive breakpoints
 * - Loading states with skeleton
 * - Accessibility support
 * - Smooth animations and transitions
 * - Performance optimized with memoization
 */
const CustomCarousel: React.FC<CustomCarouselProps> = ({
  children,
  // Embla options
  align = DEFAULT_CAROUSEL_CONFIG.align,
  loop = DEFAULT_CAROUSEL_CONFIG.loop,
  slidesToScroll = DEFAULT_CAROUSEL_CONFIG.slidesToScroll,
  containScroll = DEFAULT_CAROUSEL_CONFIG.containScroll,
  dragFree = DEFAULT_CAROUSEL_CONFIG.dragFree,
  skipSnaps = DEFAULT_CAROUSEL_CONFIG.skipSnaps,
  // UI controls
  showNavigation = DEFAULT_CAROUSEL_CONFIG.showNavigation,
  showDots = DEFAULT_CAROUSEL_CONFIG.showDots,
  // Auto-play
  autoplay = false,
  // Styling
  gap = DEFAULT_CAROUSEL_CONFIG.gap,
  className,
  containerClassName,
  slideClassName,
  // Responsive
  breakpoints = DEFAULT_BREAKPOINTS,
  // Callbacks
  onSlideChange,
  onInit,
  onDestroy,
  // Loading
  isLoading = DEFAULT_CAROUSEL_CONFIG.isLoading,
  loadingComponent,
  loadingSlides = DEFAULT_CAROUSEL_CONFIG.loadingSlides,
  // Accessibility
  navigationLabels = DEFAULT_NAVIGATION_LABELS,
  dotLabel,
  ariaLabel = "Carousel",
}) => {
  // Embla carousel setup
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align,
    loop,
    slidesToScroll,
    containScroll,
    dragFree,
    skipSnaps,
  });

  // State management
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [slideCount, setSlideCount] = useState(0);

  // Auto-play setup
  const autoplayTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [isHovered, setIsHovered] = useState(false);

  // Parse autoplay config
  const autoplayConfig: AutoplayConfig = typeof autoplay === "boolean" 
    ? { ...DEFAULT_AUTOPLAY_CONFIG, enabled: autoplay }
    : { ...DEFAULT_AUTOPLAY_CONFIG, ...autoplay };

  // Scroll handlers
  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const scrollTo = useCallback((index: number) => {
    if (emblaApi) emblaApi.scrollTo(index);
  }, [emblaApi]);

  // Selection handler
  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    
    const newIndex = emblaApi.selectedScrollSnap();
    setSelectedIndex(newIndex);
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
    
    onSlideChange?.(newIndex);
  }, [emblaApi, onSlideChange]);

  // Auto-play functionality
  const startAutoplay = useCallback(() => {
    if (!autoplayConfig.enabled || !emblaApi) return;
    
    // Respect reduced motion preference
    if (autoplayConfig.respectReducedMotion && 
        window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      return;
    }

    autoplayTimerRef.current = setInterval(() => {
      if (!isHovered || !autoplayConfig.pauseOnHover) {
        if (emblaApi.canScrollNext()) {
          emblaApi.scrollNext();
        } else if (loop) {
          emblaApi.scrollTo(0);
        }
      }
    }, autoplayConfig.delay);
  }, [autoplayConfig, emblaApi, isHovered, loop]);

  const stopAutoplay = useCallback(() => {
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current);
      autoplayTimerRef.current = null;
    }
  }, []);

  // Initialize carousel
  useEffect(() => {
    if (!emblaApi) return;

    // Set up event listeners
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);

    // Set slide count
    setSlideCount(emblaApi.slideNodes().length);

    // Initialize callback
    onInit?.(emblaApi);

    // Start autoplay
    if (autoplayConfig.enabled) {
      startAutoplay();
    }

    return () => {
      stopAutoplay();
      onDestroy?.();
    };
  }, [emblaApi, onSelect, onInit, onDestroy, autoplayConfig.enabled, startAutoplay, stopAutoplay]);

  // Handle autoplay state changes
  useEffect(() => {
    if (autoplayConfig.enabled) {
      if (isHovered && autoplayConfig.pauseOnHover) {
        stopAutoplay();
      } else {
        startAutoplay();
      }
    }
    
    return stopAutoplay;
  }, [autoplayConfig.enabled, autoplayConfig.pauseOnHover, isHovered, startAutoplay, stopAutoplay]);

  // Generate responsive CSS variables
  const responsiveStyles = React.useMemo(() => {
    const styles: React.CSSProperties = {};
    
    if (breakpoints.default?.slidesPerView) {
      styles['--slide-size'] = breakpoints.default.slidesPerView;
    }
    if (breakpoints.default?.gap) {
      styles['--slide-gap'] = breakpoints.default.gap;
    }

    return styles;
  }, [breakpoints]);

  // Loading skeleton
  const renderLoadingSkeleton = () => {
    if (!isLoading) return null;

    if (loadingComponent) {
      return loadingComponent;
    }

    return (
      <div className="flex" style={{ gap }}>
        {Array.from({ length: loadingSlides }, (_, index) => (
          <div
            key={index}
            className={cn(
              "flex-none animate-pulse",
              slideClassName
            )}
            style={{ flexBasis: breakpoints.default?.slidesPerView || "100%" }}
          >
            <div className="rounded-xl bg-gray-800/50 backdrop-blur-3xl w-full h-64 ring-1 ring-black/10" />
          </div>
        ))}
      </div>
    );
  };

  return (
    <div
      className={cn("relative w-full", className)}
      style={responsiveStyles}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      aria-label={ariaLabel}
    >
      {/* Main carousel */}
      <div
        className={cn(
          "overflow-hidden rounded-xl transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100"
        )}
        ref={emblaRef}
      >
        <div
          className={cn(
            "flex touch-pan-y",
            containerClassName
          )}
          style={{ gap }}
        >
          {React.Children.map(children, (child, index) => (
            <div
              key={index}
              className={cn(
                "flex-none min-w-0",
                slideClassName
              )}
              style={{ 
                flexBasis: breakpoints.default?.slidesPerView || "100%",
                animationDelay: `${index * 0.1}s`
              }}
            >
              {child}
            </div>
          ))}
        </div>
      </div>

      {/* Loading skeleton */}
      {renderLoadingSkeleton()}

      {/* Navigation buttons */}
      {showNavigation && !isLoading && slideCount > 1 && (
        <div className="mt-8">
          <CarouselNavigation
            onPrevious={scrollPrev}
            onNext={scrollNext}
            canScrollPrev={canScrollPrev}
            canScrollNext={canScrollNext}
            labels={navigationLabels}
          />
        </div>
      )}

      {/* Progress dots */}
      {showDots && !isLoading && slideCount > 1 && (
        <div className="mt-4">
          <CarouselDots
            slideCount={slideCount}
            selectedIndex={selectedIndex}
            onDotClick={scrollTo}
            dotLabel={dotLabel}
          />
        </div>
      )}
    </div>
  );
};

export default React.memo(CustomCarousel);

import { EmblaOptionsType } from "embla-carousel";

/**
 * Responsive breakpoint configuration for carousel slides
 */
export interface CarouselBreakpoint {
  /** Flex-basis value for slide width (e.g., "100%", "50%", "360px") */
  slidesPerView?: string;
  /** Gap between slides at this breakpoint */
  gap?: string;
}

/**
 * Responsive breakpoints configuration
 */
export interface CarouselBreakpoints {
  /** Mobile styles (default) */
  default?: CarouselBreakpoint;
  /** Small screens (640px+) */
  sm?: CarouselBreakpoint;
  /** Medium screens (768px+) */
  md?: CarouselBreakpoint;
  /** Large screens (1024px+) */
  lg?: CarouselBreakpoint;
  /** Extra large screens (1280px+) */
  xl?: CarouselBreakpoint;
}

/**
 * Navigation button labels for accessibility
 */
export interface NavigationLabels {
  /** Previous button aria-label */
  prev: string;
  /** Next button aria-label */
  next: string;
}

/**
 * Auto-play configuration
 */
export interface AutoplayConfig {
  /** Enable auto-play */
  enabled: boolean;
  /** Delay between slides in milliseconds */
  delay?: number;
  /** Pause on hover */
  pauseOnHover?: boolean;
  /** Respect user's reduced motion preference */
  respectReducedMotion?: boolean;
}

/**
 * Main props interface for CustomCarousel component
 */
export interface CustomCarouselProps {
  /** Carousel content */
  children: React.ReactNode;
  
  // Embla Carousel Options
  /** Slide alignment */
  align?: EmblaOptionsType["align"];
  /** Enable looping */
  loop?: boolean;
  /** Number of slides to scroll at once */
  slidesToScroll?: number;
  /** Contain scroll behavior */
  containScroll?: EmblaOptionsType["containScroll"];
  /** Enable drag-free scrolling */
  dragFree?: boolean;
  /** Skip snaps */
  skipSnaps?: boolean;
  
  // UI Controls
  /** Show navigation buttons */
  showNavigation?: boolean;
  /** Show progress dots */
  showDots?: boolean;
  
  // Auto-play
  /** Auto-play configuration */
  autoplay?: boolean | AutoplayConfig;
  
  // Styling
  /** Gap between slides */
  gap?: string;
  /** Additional CSS classes for carousel container */
  className?: string;
  /** Additional CSS classes for slides container */
  containerClassName?: string;
  /** Additional CSS classes for individual slides */
  slideClassName?: string;
  
  // Responsive Design
  /** Responsive breakpoint configuration */
  breakpoints?: CarouselBreakpoints;
  
  // Callbacks
  /** Called when slide changes */
  onSlideChange?: (index: number) => void;
  /** Called when carousel is initialized */
  onInit?: (emblaApi: any) => void;
  /** Called when carousel is destroyed */
  onDestroy?: () => void;
  
  // Loading State
  /** Show loading state */
  isLoading?: boolean;
  /** Custom loading component */
  loadingComponent?: React.ReactNode;
  /** Number of loading skeleton slides to show */
  loadingSlides?: number;
  
  // Accessibility
  /** Navigation button labels */
  navigationLabels?: NavigationLabels;
  /** Function to generate dot aria-label */
  dotLabel?: (index: number, total: number) => string;
  /** Carousel section aria-label */
  ariaLabel?: string;
}

/**
 * Props for CarouselNavigation component
 */
export interface CarouselNavigationProps {
  /** Scroll to previous slide */
  onPrevious: () => void;
  /** Scroll to next slide */
  onNext: () => void;
  /** Can scroll to previous slide */
  canScrollPrev: boolean;
  /** Can scroll to next slide */
  canScrollNext: boolean;
  /** Navigation labels for accessibility */
  labels?: NavigationLabels;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Props for CarouselDots component
 */
export interface CarouselDotsProps {
  /** Total number of slides */
  slideCount: number;
  /** Currently selected slide index */
  selectedIndex: number;
  /** Function to scroll to specific slide */
  onDotClick: (index: number) => void;
  /** Function to generate dot aria-label */
  dotLabel?: (index: number, total: number) => string;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Default configuration values
 */
export const DEFAULT_CAROUSEL_CONFIG: Required<
  Pick<
    CustomCarouselProps,
    | "align"
    | "loop"
    | "slidesToScroll"
    | "containScroll"
    | "dragFree"
    | "skipSnaps"
    | "showNavigation"
    | "showDots"
    | "gap"
    | "isLoading"
    | "loadingSlides"
  >
> = {
  align: "start",
  loop: false,
  slidesToScroll: 1,
  containScroll: "trimSnaps",
  dragFree: false,
  skipSnaps: false,
  showNavigation: true,
  showDots: true,
  gap: "1rem",
  isLoading: false,
  loadingSlides: 3,
};

/**
 * Default responsive breakpoints
 */
export const DEFAULT_BREAKPOINTS: CarouselBreakpoints = {
  default: { slidesPerView: "calc(100% - 2rem)" },
  sm: { slidesPerView: "calc(70% - 1rem)" },
  md: { slidesPerView: "calc(50% - 1rem)" },
  lg: { slidesPerView: "360px" },
};

/**
 * Default navigation labels
 */
export const DEFAULT_NAVIGATION_LABELS: NavigationLabels = {
  prev: "Slide anterior",
  next: "Próximo slide",
};

/**
 * Default auto-play configuration
 */
export const DEFAULT_AUTOPLAY_CONFIG: Required<AutoplayConfig> = {
  enabled: false,
  delay: 3000,
  pauseOnHover: true,
  respectReducedMotion: true,
};

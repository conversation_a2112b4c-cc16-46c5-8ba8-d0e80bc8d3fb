# Linktree Extractor

Este módulo utiliza a API do Firecrawl para extrair dados estruturados de páginas do Linktree.

## Correções Implementadas

### 1. **Configuração da API Key**
- ❌ **Problema**: API key hardcoded no código
- ✅ **Solução**: Uso de variáveis de ambiente (`FIRECRAWL_API_KEY` ou `NEXT_PUBLIC_FIRECRAWL_API_KEY`)

### 2. **Schema Correction**
- ❌ **Problema**: Uso incorreto do schema Zod diretamente na API do Firecrawl
- ✅ **Solução**: Conversão do schema Zod para JSON Schema format conforme documentação do Firecrawl v1

### 3. **Error Handling**
- ❌ **Problema**: Tratamento de erro limitado
- ✅ **Solução**: Tratamento robusto de erros com validação de dados e mensagens claras

### 4. **Type Safety**
- ❌ **Problema**: Tipos não definidos para o retorno da função
- ✅ **Solução**: Interface `LinktreeExtractionResult` com tipagem completa

## Configuração

### 1. Variáveis de Ambiente

Adicione sua API key do Firecrawl no arquivo `.env`:

```env
# Firecrawl API Configuration
FIRECRAWL_API_KEY=fc-your-api-key-here
NEXT_PUBLIC_FIRECRAWL_API_KEY=fc-your-api-key-here
```

### 2. Obter API Key

1. Acesse [https://firecrawl.dev/app](https://firecrawl.dev/app)
2. Crie uma conta ou faça login
3. Copie sua API key
4. Adicione no arquivo `.env`

## Uso

```typescript
import { extractLinktreeData } from './lib/linktreeExtractor';

async function example() {
  const result = await extractLinktreeData('https://linktr.ee/username');
  
  if (result.success && result.data) {
    console.log('Links encontrados:', result.data.links.length);
    console.log('Username:', result.data.extra_info?.username);
    
    result.data.links.forEach(link => {
      console.log(`${link.title}: ${link.url}`);
    });
  } else {
    console.error('Erro:', result.error);
  }
}
```

## Estrutura de Dados

```typescript
interface LinktreeExtractionResult {
  success: boolean;
  data?: {
    extra_info?: {
      location?: string;
      username?: string;
      description?: string;
      avatar_url?: string;
      user_bio?: string;
    };
    links: Array<{
      title?: string;
      url: string;
      icon?: string;
    }>;
  };
  error?: string;
}
```

## Testes

Execute os testes disponíveis:

```bash
# Teste completo
npx tsx test-extractor.ts

# Teste simples direto com Firecrawl
node test-simple.mjs
```

## Documentação Firecrawl

Para mais informações sobre a API do Firecrawl, consulte:
- [Documentação Extract](https://docs.firecrawl.dev/features/extract)
- [API Reference](https://docs.firecrawl.dev/api-reference/endpoint/extract)

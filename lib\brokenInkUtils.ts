import { UserProfile } from '@/types/user'

// ============================================================================
// BROKEN INK DATA TRANSFORMATION UTILITIES
// ============================================================================

/**
 * Transform UserProfile data for HeroSection component
 */
export function transformHeroData(profile: UserProfile) {
  return {
    title: profile.user.name || 'Tattoo Studio',
    description: profile.user.bio || 'Confira nossos links.',
    backgroundImage: profile.user.heroImage || profile.settings.ogImage || profile.user.avatar || 'https://db.avenca.cloud/images/2025/07/01/tattoo4.jpg',
    buttonText: 'Agendar',
    colors: profile.settings?.colors
  }
}

/**
 * Generate custom description based on platform type
 */
function getCustomDescription(url: string, platformName: string): string {
  const lowerUrl = url.toLowerCase();
  const lowerPlatformName = platformName.toLowerCase();

  // WhatsApp
  if (lowerUrl.includes('whatsapp') || lowerUrl.includes('wa.me') ||
    lowerPlatformName.includes('whatsapp')) {
    return "Enviar mensagem no WhatsApp";
  }

  // Instagram
  if (lowerUrl.includes('instagram.com') || lowerPlatformName.includes('instagram')) {
    return "Confira nosso Instagram";
  }

  // Phone numbers (tel: protocol or phone-like patterns)
  if (lowerUrl.startsWith('tel:') || lowerPlatformName.includes('telefone') ||
    lowerPlatformName.includes('phone') || lowerPlatformName.includes('contato')) {
    return "Entre em contato";
  }

  // Email
  if (lowerUrl.startsWith('mailto:') || lowerPlatformName.includes('email') ||
    lowerPlatformName.includes('e-mail')) {
    return "Enviar email";
  }

  // Facebook
  if (lowerUrl.includes('facebook.com') || lowerPlatformName.includes('facebook')) {
    return "Visite nosso Facebook";
  }

  // Twitter/X
  if (lowerUrl.includes('twitter.com') || lowerUrl.includes('x.com') ||
    lowerPlatformName.includes('twitter') || lowerPlatformName.includes('x.com')) {
    return "Siga no Twitter";
  }

  // TikTok
  if (lowerUrl.includes('tiktok.com') || lowerPlatformName.includes('tiktok')) {
    return "Veja nossos vídeos no TikTok";
  }

  // YouTube
  if (lowerUrl.includes('youtube.com') || lowerPlatformName.includes('youtube')) {
    return "Assista nosso canal no YouTube";
  }

  // LinkedIn
  if (lowerUrl.includes('linkedin.com') || lowerPlatformName.includes('linkedin')) {
    return "Conecte-se no LinkedIn";
  }

  // Pinterest
  if (lowerUrl.includes('pinterest.com') || lowerPlatformName.includes('pinterest')) {
    return "Veja nossos pins no Pinterest";
  }

  // Default: just return the platform name
  return platformName;
}

/**
 * Transform UserProfile socialMedia for SocialMediaSection component
 */
export function transformSocialMediaData(profile: UserProfile) {
  const defaultPlatforms = [
    {
      iconName: "instagram",
      platformName: "Instagram",
      description: "Confira nosso Instagram",
      href: "#",
    },
    {
      iconName: "facebook",
      platformName: "Facebook",
      description: "Visite nosso Facebook",
      href: "#",
    },
    {
      iconName: "twitter",
      platformName: "Twitter",
      description: "Siga no Twitter",
      href: "#",
    },
  ]

  // Transform user's social media links
  const userSocialPlatforms = profile.socialMedia?.map(social => ({
    iconName: getIconNameFromUrl(social.url) || extractIconFromClass(social.classIcon),
    platformName: social.text,
    description: getCustomDescription(social.url, social.text),
    href: social.url,
  })) || []

  return {
    title: "Links e Redes Sociais",
    description: "Confira nossos links e acompanhe nossos trabalhos mais recentes.",
    socialPlatforms: userSocialPlatforms.length > 0 ? userSocialPlatforms : defaultPlatforms,
    colors: profile.settings?.colors
  }
}

/**
 * Transform UserProfile servicesSection for ServicesSection component
 */
export function transformServicesData(profile: UserProfile) {
  const defaultServices = [
    {
      iconName: "brush",
      title: "Tatuagens Personalizadas",
      description: "Dê vida à sua visão com uma peça única desenhada por nossos artistas.",
    },
    {
      iconName: "palette",
      title: "Designs Prontos",
      description: "Escolha entre nossa coleção curada de designs pré-desenhados, prontos para serem tatuados.",
    },
    {
      iconName: "refresh",
      title: "Cover-Ups",
      description: "Expertly conceal or transform old tattoos with our creative cover-up solutions.",
    },
  ]

  // Use user's services if available and enabled
  const userServices = profile.servicesSection?.enabled && profile.servicesSection.items?.length > 0
    ? profile.servicesSection.items.map(service => ({
      title: service.title || 'Service',
      description: service.description || 'Professional service',
    }))
    : defaultServices

  return {
    title: profile.servicesSection?.title || "Serviços",
    description: profile.servicesSection?.description || "Oferecemos diversos serviços.",
    services: userServices,
    enabled: profile.servicesSection?.enabled !== false,
    colors: profile.settings?.colors
  }
}

/**
 * Transform UserProfile gallery for GallerySection component
 */
export function transformGalleryData(profile: UserProfile) {
  const defaultGalleryItems = [
    {
      imageUrl: "https://lh3.googleusercontent.com/aida-public/AB6AXuA7w9N14rC0kpL-oKDNdXB2RBbbEt3bkkAiMUw90ilcNsio4NdErcvCW2QA-2GTo0gF6ll45gnFUEm0ISiGtfBJx_FPWaV_pyVQ9fjaqYtI1sbS0ZF9Nq_YQSudhoq1FFZf9ewMQSMDgJce2ILmSFKUuK-DSJdyJagpWJ46zn2WDJ1KDRBGK2CjR-jqKnRodrSmUDsppCbirxG8BzR1IL58nc1_FUpSTkfR9fHkON8tK6Zgu5alEcsQmsrPtSY-iEhJxTNkwrorqKM",
      altText: "Intricate Sleeve Tattoo",
      title: "Intricate Sleeve"
    },
  ]

  // Use user's gallery if available and enabled
  const userGalleryItems = profile.gallery?.enabled && profile.gallery.images?.length > 0
    ? profile.gallery.images.map(image => ({
      imageUrl: image.url || '',
      altText: image.alt || image.title || "Gallery Image",
      title: image.title || "Tattoo Work"
    }))
    : defaultGalleryItems

  return {
    title: profile.gallery?.title || "Nossa Galeria",
    description: profile.gallery?.description || "Explore nossos trabalhos mais recentes e inspire-se.",
    galleryItems: userGalleryItems,
    enabled: profile.gallery?.enabled !== false,
    colors: profile.settings?.colors
  }
}

/**
 * Transform UserProfile team for TeamSection component
 */
export function transformTeamData(profile: UserProfile) {
  const defaultTeam = [
    {
      imageUrl: "https://db.avenca.cloud/images/2025/07/19/image.png",
      name: "Seu Nome",
      specialty: "Sua Especialidade",
      url: undefined
    },
  ]

  // Use user's team members if available and enabled
  const userTeam = profile.team?.enabled && profile.team.members?.length > 0
    ? profile.team.members.map(member => ({
      imageUrl: member.photo || profile.user.avatar || '',
      name: member.name || 'Seu Nome',
      specialty: member.role || "Especialidade",
      url: member.url || undefined
    }))
    : defaultTeam

  return {
    title: profile.team?.title || "Nossa Equipe",
    artists: userTeam,
    enabled: profile.team?.enabled !== false,
    colors: profile.settings?.colors
  }
}

/**
 * Transform UserProfile location for LocationSection component
 */
export function transformLocationData(profile: UserProfile) {
  // Format address object into a string if it exists
  const formatAddress = (address: string | { street?: string; city?: string; state?: string; zipCode?: string; country?: string } | null | undefined) => {
    if (!address) return null;

    if (typeof address === 'string') {
      return address;
    }

    if (typeof address === 'object') {
      const parts = [];
      if (address.street) parts.push(address.street);
      if (address.city) parts.push(address.city);
      if (address.state) parts.push(address.state);
      if (address.zipCode) parts.push(address.zipCode);
      if (address.country) parts.push(address.country);
      return parts.join(', ');
    }

    return null;
  };

  return {
    enabled: profile.location?.enabled || false,
    title: "Localização",
    description: "Venha nos visitar em nosso estabelecimento",
    address: formatAddress(profile.location?.address),
    contact: profile.location?.contact || null,
    hours: profile.location?.hours || null,
    googleMapsUrl: profile.location?.googleMapsUrl || null,
    colors: profile.settings?.colors
  }
}

/**
 * Check if a link should be filtered out (social media, phone, location)
 */
function shouldFilterLink(url: string, classIcon: string): boolean {
  // Filter social media links
  const socialPlatforms = ['instagram.com', 'facebook.com', 'twitter.com', 'x.com', 'tiktok.com', 'pinterest.com', 'linkedin.com', 'youtube.com'];
  if (socialPlatforms.some(platform => url.includes(platform))) {
    return true;
  }

  // Filter phone and WhatsApp links
  if (url.startsWith('tel:') || url.startsWith('phone:') || url.includes('whatsapp') || url.includes('wa.me')) {
    return true;
  }

  // Filter location/maps links
  if (url.includes('maps.') || url.includes('goo.gl') || url.includes('google.com/maps')) {
    return true;
  }

  // Filter by icon class
  const socialIconClasses = ['instagram', 'facebook', 'twitter', 'tiktok', 'pinterest', 'linkedin', 'youtube', 'whatsapp', 'phone', 'mobile', 'map', 'location'];
  if (socialIconClasses.some(iconClass => classIcon.includes(iconClass))) {
    return true;
  }

  return false;
}

/**
 * Transform UserProfile links for LinksSection component
 */
export function transformLinksData(profile: UserProfile) {
  const defaultLinks = [
    {
      iconName: "link",
      text: "Nosso Site",
      description: "Visite nosso site oficial",
      url: "#",
    },
    {
      iconName: "calendar",
      text: "Agendar",
      description: "Agende seu horário",
      url: "#",
    },
    {
      iconName: "star",
      text: "Avaliações",
      description: "Veja nossas avaliações",
      url: "#",
    },
  ]

  // Transform and filter user's links
  const userLinks = profile.links?.filter(link =>
    !shouldFilterLink(link.url, link.classIcon)
  ).map(link => ({
    iconName: getIconNameFromUrl(link.url) || extractIconFromClass(link.classIcon),
    text: link.text,
    description: getLinkDescription(link.url, link.text),
    url: link.url,
  })) || []

  return {
    title: "Links Importantes",
    description: "Acesse nossos principais links e informações.",
    links: userLinks.length > 0 ? userLinks : defaultLinks,
    colors: profile.settings?.colors
  }
}

/**
 * Transform UserProfile contact data for ContactSection component
 */
export function transformContactData(profile: UserProfile) {
  return {
    phone: profile.phone,
    links: profile.links || [],
    socialMedia: profile.socialMedia || [],
    colors: profile.settings?.colors
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Extract icon name from social media URL
 */
function getIconNameFromUrl(url: string): string | null {
  if (url.includes('instagram.com')) return 'instagram'
  if (url.includes('facebook.com')) return 'facebook'
  if (url.includes('twitter.com') || url.includes('x.com')) return 'twitter'
  if (url.includes('tiktok.com')) return 'tiktok'
  if (url.includes('pinterest.com')) return 'pinterest'
  if (url.includes('linkedin.com')) return 'linkedin'
  if (url.includes('youtube.com')) return 'youtube'
  if (url.includes('whatsapp') || url.includes('wa.me')) return 'whatsapp'
  if (url.startsWith('tel:') || url.startsWith('phone:')) return 'phone'
  if (url.startsWith('mailto:')) return 'mail'
  return null
}

/**
 * Extract icon name from Font Awesome class
 */
function extractIconFromClass(classIcon: string): string {
  if (!classIcon) return 'globe'

  // Extract icon name from classes like "fa fa-instagram" or "fab fa-instagram"
  const match = classIcon.match(/fa-([a-zA-Z0-9-]+)/)
  const iconName = match ? match[1] : 'globe'

  // Map common Font Awesome icon names to our icon system
  const iconMap: { [key: string]: string } = {
    'phone': 'phone',
    'phone-alt': 'phone',
    'mobile': 'phone',
    'mobile-alt': 'phone',
    'envelope': 'mail',
    'envelope-open': 'mail',
    'at': 'mail',
    'whatsapp': 'whatsapp',
    'instagram': 'instagram',
    'facebook': 'facebook',
    'facebook-f': 'facebook',
    'twitter': 'twitter',
    'tiktok': 'tiktok',
    'pinterest': 'pinterest',
    'pinterest-p': 'pinterest',
    'linkedin': 'linkedin',
    'linkedin-in': 'linkedin',
    'youtube': 'youtube'
  }

  return iconMap[iconName] || iconName
}

/**
 * Get description for general links
 */
function getLinkDescription(url: string, text: string): string {
  if (url.startsWith('tel:')) return `Ligar para ${text}`
  if (url.startsWith('mailto:')) return `Enviar email para ${text}`
  if (url.startsWith('https://wa.me') || url.includes('whatsapp')) return `Conversar no WhatsApp`
  if (url.includes('maps.') || url.includes('goo.gl')) return `Ver localização no mapa`
  if (url.startsWith('#')) return `Ir para seção ${text}`
  if (url.startsWith('http')) return `Acessar ${text}`
  return `Acesse ${text}`
}

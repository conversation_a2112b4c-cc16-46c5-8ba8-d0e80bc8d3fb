import { UserProfile, ApiResponse, validateUserProfile } from '@/types'

// Define the structure for different data sections
export interface DataSection {
  id: string
  name: string
  type: 'array' | 'object'
  fields: DataField[]
  itemsKey?: string // Key name for the items array (e.g., 'items', 'images', 'reviews', 'members')
}

export interface DataField {
  key: string
  label: string
  type: 'string' | 'number' | 'boolean' | 'url' | 'email' | 'textarea' | 'select'
  required?: boolean
  options?: string[] // For select fields
  validation?: (value: any) => string | null
}

// Data section definitions based on the JSON structure
export const dataSections: DataSection[] = [
  {
    id: 'featuresSection',
    name: 'Features',
    type: 'object',
    itemsKey: 'items',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'gallery',
    name: 'Gallery',
    type: 'object',
    itemsKey: 'images',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'servicesSection',
    name: 'Services',
    type: 'object',
    itemsKey: 'items',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'reviews',
    name: 'Reviews',
    type: 'object',
    itemsKey: 'reviews',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'team',
    name: 'Team',
    type: 'object',
    itemsKey: 'members',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
    ]
  },
  {
    id: 'links',
    name: 'Links',
    type: 'array',
    fields: [
      { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
      { key: 'text', label: 'Text', type: 'string', required: true },
      { key: 'url', label: 'URL', type: 'url', required: true },
    ]
  },
  {
    id: 'socialMedia',
    name: 'Social Media',
    type: 'array',
    fields: [
      { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
      { key: 'text', label: 'Text', type: 'string', required: true },
      { key: 'url', label: 'URL', type: 'url', required: true },
    ]
  },
  {
    id: 'location',
    name: 'Location',
    type: 'object',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'address.street', label: 'Street Address', type: 'string', required: true },
      { key: 'address.city', label: 'City', type: 'string', required: true },
      { key: 'address.state', label: 'State/Province', type: 'string', required: true },
      { key: 'address.zipCode', label: 'ZIP/Postal Code', type: 'string', required: true },
      { key: 'address.country', label: 'Country', type: 'string', required: true },
      {
        key: 'contact.phone',
        label: 'Phone Number',
        type: 'string',
        validation: (value: string) => {
          if (value && !/^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/[\s\-\(\)]/g, ''))) {
            return 'Please enter a valid phone number';
          }
          return null;
        }
      },
      { key: 'contact.whatsapp', label: 'WhatsApp URL', type: 'url' },
      { key: 'hours.weekdays', label: 'Weekday Hours', type: 'string' },
      { key: 'hours.weekends', label: 'Weekend Hours', type: 'string' },
      { key: 'hours.closed', label: 'Closed Days', type: 'string' },
      { key: 'googleMapsUrl', label: 'Google Maps URL', type: 'url' },
    ]
  },
  {
    id: 'settings',
    name: 'Settings',
    type: 'object',
    fields: [
      {
        key: 'colors.background',
        label: 'Background Color',
        type: 'string',
        validation: (value: string) => {
          if (value && !/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)) {
            return 'Please enter a valid hex color (e.g., #FF0000)';
          }
          return null;
        }
      },
      {
        key: 'colors.linkText',
        label: 'Link Text Color',
        type: 'string',
        validation: (value: string) => {
          if (value && !/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)) {
            return 'Please enter a valid hex color (e.g., #FF0000)';
          }
          return null;
        }
      },
      {
        key: 'colors.primary',
        label: 'Primary Color',
        type: 'string',
        validation: (value: string) => {
          if (value && !/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)) {
            return 'Please enter a valid hex color (e.g., #FF0000)';
          }
          return null;
        }
      },
      {
        key: 'colors.secondary',
        label: 'Secondary Color',
        type: 'string',
        validation: (value: string) => {
          if (value && !/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)) {
            return 'Please enter a valid hex color (e.g., #FF0000)';
          }
          return null;
        }
      },
      {
        key: 'colors.socialIconBackground',
        label: 'Social Icon Background',
        type: 'string',
        validation: (value: string) => {
          if (value && !/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)) {
            return 'Please enter a valid hex color (e.g., #FF0000)';
          }
          return null;
        }
      },
      { key: 'favicon', label: 'Favicon URL', type: 'url' },
      {
        key: 'pageDescription',
        label: 'Page Description',
        type: 'textarea',
        validation: (value: string) => {
          if (value && value.length > 160) {
            return 'Page description should be 160 characters or less for SEO';
          }
          return null;
        }
      },
      {
        key: 'pageKeywords',
        label: 'Page Keywords',
        type: 'string',
        validation: (value: string) => {
          if (value && value.split(',').length > 10) {
            return 'Please limit to 10 keywords or less, separated by commas';
          }
          return null;
        }
      },
    ]
  },
]

// Item field definitions for array-type sections
export const itemFieldDefinitions: Record<string, DataField[]> = {
  featuresSection: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'image', label: 'Image URL', type: 'url', required: true },
  ],
  gallery: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'alt', label: 'Alt Text', type: 'string', required: true },
    { key: 'url', label: 'Image URL', type: 'url', required: true },
  ],
  servicesSection: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'image', label: 'Image URL', type: 'url', required: true },
  ],
  reviews: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'name', label: 'Name', type: 'string', required: true },
    { key: 'comment', label: 'Comment', type: 'textarea', required: true },
    { key: 'rating', label: 'Rating', type: 'number', required: true },
    { key: 'photo', label: 'Photo URL', type: 'url' },
  ],
  team: [
    { key: 'name', label: 'Name', type: 'string', required: true },
    { key: 'role', label: 'Role', type: 'string', required: true },
    { key: 'photo', label: 'Photo URL', type: 'url' },
    { key: 'url', label: 'Profile URL', type: 'url' },
  ],
  links: [
    { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
    { key: 'text', label: 'Text', type: 'string', required: true },
    { key: 'url', label: 'URL', type: 'url', required: true },
  ],
  socialMedia: [
    { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
    { key: 'text', label: 'Text', type: 'string', required: true },
    { key: 'url', label: 'URL', type: 'url', required: true },
  ],
}

export interface CrudOperation {
  type: 'create' | 'read' | 'update' | 'delete'
  section: string
  itemId?: string | number
  data?: any
}

export interface CrudResult<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

class AdminDataService {
  private dataCache = new Map<string, UserProfile>()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
  private readonly API_BASE_URL = process.env.NEXT_PUBLIC_FIREBASE_API_BASE_URL || 'https://cardlink-bio-default-rtdb.firebaseio.com/users'

  /**
   * Get all available usernames/profiles from Firebase
   */
  async getAvailableProfiles(): Promise<string[]> {
    try {
      const response = await fetch(`${this.API_BASE_URL}.json`)
      if (!response.ok) {
        throw new Error(`Failed to fetch users: ${response.status}`)
      }

      // Safely parse JSON response
      let data
      try {
        const contentType = response.headers.get('content-type')
        if (contentType && contentType.includes('application/json')) {
          data = await response.json()
        } else {
          console.warn(`Expected JSON but received ${contentType} when fetching users`)
          return []
        }
      } catch (parseError) {
        console.error('Failed to parse JSON response when fetching users:', parseError)
        return []
      }
      if (!data) {
        return []
      }

      // Return the keys (usernames) from the Firebase response
      return Object.keys(data)
    } catch (error) {
      console.error('Error fetching available profiles:', error)
      return []
    }
  }

  /**
   * Load user profile data from Firebase
   */
  async loadUserData(username: string): Promise<UserProfile | null> {
    try {
      // Check cache first
      const cached = this.dataCache.get(username)
      if (cached) {
        return cached
      }

      const response = await fetch(`${this.API_BASE_URL}/${username}.json`)
      if (!response.ok) {
        if (response.status === 404) {
          return null
        }
        throw new Error(`Failed to load data: ${response.status}`)
      }

      const data = await response.json()
      if (!data) {
        return null
      }

      const validationResult = validateUserProfile(data)

      if (!validationResult.isValid) {
        console.warn('Data validation warnings:', validationResult.errors)
      }

      // Cache the data
      this.dataCache.set(username, data)

      return data
    } catch (error) {
      console.error('Error loading user data:', error)
      return null
    }
  }

  /**
   * Save user profile data to Firebase
   */
  async saveUserData(username: string, data: UserProfile): Promise<CrudResult> {
    try {
      const response = await fetch(`${this.API_BASE_URL}/${username}.json`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`Failed to save data: ${response.status}`)
      }

      // Update cache
      this.dataCache.set(username, data)

      return { success: true, message: 'Data saved successfully' }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save data'
      }
    }
  }

  /**
   * Get data for a specific section
   */
  async getSectionData(username: string, sectionId: string): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      const sectionData = (userData as any)[sectionId]
      if (sectionData === undefined) {
        return { success: false, error: 'Section not found' }
      }

      return { success: true, data: sectionData }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get items from an array-type section
   */
  async getSectionItems(username: string, sectionId: string): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      // Find the section configuration
      const sectionConfig = dataSections.find(section => section.id === sectionId)

      // For sections that are direct arrays in the root (like links, socialMedia)
      if (sectionConfig && !sectionConfig.itemsKey) {
        const items = (userData as any)[sectionId]

        // Ensure we return an array
        if (Array.isArray(items)) {
          return { success: true, data: items }
        } else if (items === null || items === undefined) {
          return { success: true, data: [] }
        } else {
          // If it's not an array, wrap it or return empty array
          console.warn(`Section ${sectionId} is not an array:`, items)
          return { success: true, data: [] }
        }
      }

      // For sections that have nested items (like featuresSection.items)
      const section = (userData as any)[sectionId]
      if (!section) {
        return { success: false, error: 'Section not found' }
      }

      const itemsKey = sectionConfig?.itemsKey || 'items'
      const items = section[itemsKey] || []
      return { success: true, data: items }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create a new item in a section
   */
  async createItem(username: string, sectionId: string, itemData: any): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      // Find the section configuration
      const sectionConfig = dataSections.find(s => s.id === sectionId)

      // For sections that are direct arrays in the root (like links, socialMedia)
      if (sectionConfig && !sectionConfig.itemsKey) {
        let items = (userData as any)[sectionId]

        // Ensure we have an array to work with
        if (!Array.isArray(items)) {
          items = []
        }

        // Generate new ID for items that need it
        if (itemData.id === undefined && items.length > 0) {
          const maxId = Math.max(0, ...items.map((item: any) => item.id || 0))
          itemData.id = maxId + 1
        } else if (itemData.id === undefined) {
          itemData.id = 1
        }

        items.push(itemData)
          ; (userData as any)[sectionId] = items

        // Save to Firebase
        const saveResult = await this.saveUserData(username, userData)
        if (!saveResult.success) {
          return saveResult
        }

        return {
          success: true,
          data: itemData,
          message: 'Item created successfully'
        }
      }

      // For sections that have nested items (like featuresSection.items)
      const section = (userData as any)[sectionId]
      if (!section) {
        return { success: false, error: 'Section not found' }
      }

      const itemsKey = sectionConfig?.itemsKey || 'items'

      // Generate new ID for items that need it
      if (itemData.id === undefined && section[itemsKey]) {
        const maxId = Math.max(0, ...section[itemsKey].map((item: any) => item.id || 0))
        itemData.id = maxId + 1
      }

      // Add item to section
      if (section[itemsKey]) {
        section[itemsKey].push(itemData)
      }

      // Save to Firebase
      const saveResult = await this.saveUserData(username, userData)
      if (!saveResult.success) {
        return saveResult
      }

      return {
        success: true,
        data: itemData,
        message: 'Item created successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Update an existing item
   */
  async updateItem(username: string, sectionId: string, itemId: string | number, itemData: any): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      const section = (userData as any)[sectionId]
      if (!section) {
        return { success: false, error: 'Section not found' }
      }

      // Find the section configuration to get the correct items key
      const sectionConfig = dataSections.find(s => s.id === sectionId)
      const itemsKey = sectionConfig?.itemsKey || 'items'

      if (!section[itemsKey]) {
        return { success: false, error: 'Items array not found' }
      }

      const itemIndex = section[itemsKey].findIndex((item: any) => item.id === itemId)
      if (itemIndex === -1) {
        return { success: false, error: 'Item not found' }
      }

      // Update the item
      section[itemsKey][itemIndex] = { ...section[itemsKey][itemIndex], ...itemData }

      // Save to Firebase
      const saveResult = await this.saveUserData(username, userData)
      if (!saveResult.success) {
        return saveResult
      }

      return {
        success: true,
        data: section[itemsKey][itemIndex],
        message: 'Item updated successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Delete an item
   */
  async deleteItem(username: string, sectionId: string, itemId: string | number): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      const section = (userData as any)[sectionId]
      if (!section) {
        return { success: false, error: 'Section not found' }
      }

      // Find the section configuration to get the correct items key
      const sectionConfig = dataSections.find(s => s.id === sectionId)
      const itemsKey = sectionConfig?.itemsKey || 'items'

      if (!section[itemsKey]) {
        return { success: false, error: 'Items array not found' }
      }

      const itemIndex = section[itemsKey].findIndex((item: any) => item.id === itemId)
      if (itemIndex === -1) {
        return { success: false, error: 'Item not found' }
      }

      // Remove the item
      const deletedItem = section[itemsKey].splice(itemIndex, 1)[0]

      // Save to Firebase
      const saveResult = await this.saveUserData(username, userData)
      if (!saveResult.success) {
        return saveResult
      }

      return {
        success: true,
        data: deletedItem,
        message: 'Item deleted successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Validate item data against field definitions
   */
  validateItemData(sectionId: string, itemData: any): { isValid: boolean; errors: string[] } {
    const fields = itemFieldDefinitions[sectionId] || []
    const errors: string[] = []

    for (const field of fields) {
      const value = itemData[field.key]

      // Check required fields
      if (field.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field.label} is required`)
        continue
      }

      // Type validation
      if (value !== undefined && value !== null && value !== '') {
        switch (field.type) {
          case 'number':
            if (isNaN(Number(value))) {
              errors.push(`${field.label} must be a number`)
            }
            break
          case 'url':
            try {
              new URL(value)
            } catch {
              errors.push(`${field.label} must be a valid URL`)
            }
            break
          case 'email':
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
              errors.push(`${field.label} must be a valid email`)
            }
            break
        }

        // Custom validation
        if (field.validation) {
          const validationError = field.validation(value)
          if (validationError) {
            errors.push(validationError)
          }
        }
      }
    }

    return { isValid: errors.length === 0, errors }
  }
}

export const adminDataService = new AdminDataService()
